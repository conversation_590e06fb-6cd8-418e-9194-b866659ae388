<?php

namespace App\Http\Controllers\Api\Main;

use App\Http\Controllers\Controller;
use App\Models\Api\Main\Day;
use App\Models\Api\Main\Group;
use App\Models\Api\Main\Lessen;
use App\Models\Api\Users\Student;
use App\Models\Api\Users\Teacher;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class GroupsController extends Controller
{
    public function index(Request $request)
    {
        $query = Group::with(['section.year.department'])
            ->withCount('students');

        // Filter by year if provided
        if ($request->has('year') && $request->year) {
            $query->whereHas('section.year', function ($q) use ($request) {
                $q->where('id', $request->year);
            });
        }

        $groups = $query->paginate(6);

        return response()->json($groups);
    }

    /**
     * Store a newly created group in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'number' => 'required|integer|min:1',
            'section_id' => 'required|exists:sections,id',
        ]);

        // Check if group with this number already exists for this section
        $existingGroup = Group::where('number', $request->number)
            ->where('section_id', $request->section_id)
            ->first();

        if ($existingGroup) {
            return response()->json([
                'message' => 'A group with this number already exists for the selected section.'
            ], 422);
        }

        $group = Group::create([
            'number' => $request->number,
            'section_id' => $request->section_id,
        ]);

        // Create a time table for the group using polymorphic relationship
        $timeTable = $group->timeTable()->create([]);

        // Create days for the time table (Monday to Sunday)
        $dayNames = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];
        foreach ($dayNames as $dayName) {
            Day::create([
                'name' => $dayName,
                'time_table_id' => $timeTable->id,
            ]);
        }

        return response()->json($group->load(['section.year.department', 'timeTable.days']), 201);
    }

    /**
     * Display the specified group.
     */
    public function show(Group $group)
    {
        return response()->json($group->load(['section.year.department', 'timeTable.days.lessens.classRome']));
    }

    /**
     * Update the specified group in storage.
     */
    public function update(Request $request, Group $group)
    {
        $request->validate([
            'number' => 'required|integer|min:1',
            'section_id' => 'required|exists:sections,id',
        ]);

        // Check if another group with this number already exists for this section
        $existingGroup = Group::where('number', $request->number)
            ->where('section_id', $request->section_id)
            ->where('id', '!=', $group->id)
            ->first();

        if ($existingGroup) {
            return response()->json([
                'message' => 'A group with this number already exists for the selected section.'
            ], 422);
        }

        $group->update([
            'number' => $request->number,
            'section_id' => $request->section_id,
        ]);

        return response()->json($group->load(['section.year.department', 'timeTable.days.lessens.classRome']));
    }

    /**
     * Remove the specified group from storage.
     */
    public function destroy(Group $group)
    {
        // Delete the group's timetable and associated days/lessons
        if ($group->timeTable) {
            $group->timeTable->days()->each(function ($day) {
                $day->lessens()->delete();
            });
            $group->timeTable->days()->delete();
            $group->timeTable->delete();
        }

        $group->delete();

        return response()->json([
            'message' => 'Group deleted successfully'
        ]);
    }

    public function students()
    {
        $student = Student::find(Auth::user()->key->keyable_id);

        if (!$student) {
            return response()->json(['error' => 'Student not found'], 404);
        }

        $group = $student->group;
        if (!$group) {
            return response()->json(['error' => 'Student has no group assigned'], 404);
        }

        $section = $group->section;
        if (!$section) {
            return response()->json(['error' => 'Group has no section assigned'], 404);
        }

        $timeTableGroup = $group->timeTable;
        if (!$timeTableGroup) {
            return response()->json(['error' => 'Group has no timetable'], 404);
        }

        $timeTableSection = $section->timeTable;
        if (!$timeTableSection) {
            return response()->json(['error' => 'Section has no timetable'], 404);
        }

        \Log::info('Student Timetable Data:', [
            'student_id' => $student->id,
            'group_id' => $group->id,
            'section_id' => $section->id,
            'group_timetable_id' => $timeTableGroup->id,
            'section_timetable_id' => $timeTableSection->id
        ]);

        $groupTimetable = $timeTableGroup->load(
            [
                'days' => [
                    'lessens' => function ($query) {
                        $query->orderBy('start_time')
                            ->with(['classRome', 'module', 'teacher']);
                    }
                ]
            ]
        );

        $sectionTimetable = $timeTableSection->load(
            [
                'days' => [
                    'lessens' => function ($query) {
                        $query->orderBy('start_time')
                            ->with(['classRome', 'module', 'teacher']);
                    }
                ]
            ]
        );

        // Count lessons for debugging
        $groupLessonsCount = $groupTimetable->days->sum(function ($day) {
            return $day->lessens->count();
        });

        $sectionLessonsCount = $sectionTimetable->days->sum(function ($day) {
            return $day->lessens->count();
        });

        \Log::info('Loaded Timetables:', [
            'group_days_count' => $groupTimetable->days->count(),
            'section_days_count' => $sectionTimetable->days->count(),
            'group_lessons_count' => $groupLessonsCount,
            'section_lessons_count' => $sectionLessonsCount
        ]);

        return response()->json(
            [
                'timeTableGroup' => $groupTimetable,
                'timeTableSection' => $sectionTimetable,
                'groupInfo' => [
                    'id' => $group->id,
                    'number' => $group->number,
                    'section_id' => $group->section_id,
                ],
                'sectionInfo' => [
                    'id' => $section->id,
                    'number' => $section->number,
                    'year_id' => $section->year_id,
                ],
            ]
        );
    }

    public function teacher()
    {
        $teacher = Teacher::find(Auth::user()->key->keyable_id);

        if (!$teacher) {
            return response()->json(['error' => 'Teacher not found'], 404);
        }

        \Log::info('Fetching teacher timing data', ['teacher_id' => $teacher->id]);

        // Get all lessons for this teacher, grouped by day
        $lessons = Lessen::where('teacher_id', $teacher->id)
            ->with(['day', 'classRome', 'module', 'teacher'])
            ->orderBy('start_time')
            ->get()
            ->groupBy(function ($lesson) {
                return $lesson->day ? $lesson->day->name : null;
            });

        // Initialize response structure for all days
        $dayNames = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];
        $response = [];

        foreach ($dayNames as $dayName) {
            if (isset($lessons[$dayName]) && $lessons[$dayName]->isNotEmpty()) {
                // Create a day object with lessons
                $dayLessons = $lessons[$dayName];
                $firstLesson = $dayLessons->first();

                $response[$dayName === 'tue' ? 'tues' : $dayName] = [
                    'id' => $firstLesson->day->id,
                    'name' => $dayName,
                    'created_at' => $firstLesson->day->created_at,
                    'updated_at' => $firstLesson->day->updated_at,
                    'time_table_id' => $firstLesson->day->time_table_id,
                    'lessens' => $dayLessons->map(function ($lesson) {
                        return [
                            'id' => $lesson->id,
                            'start_time' => $lesson->start_time,
                            'end_time' => $lesson->end_time,
                            'type' => $lesson->type,
                            'created_at' => $lesson->created_at,
                            'updated_at' => $lesson->updated_at,
                            'day_id' => $lesson->day_id,
                            'module_id' => $lesson->module_id,
                            'teacher_id' => $lesson->teacher_id,
                            'class_rome_id' => $lesson->class_rome_id,
                            'class_rome' => $lesson->classRome,
                            'module' => $lesson->module,
                            'teacher' => $lesson->teacher,
                        ];
                    })->toArray()
                ];
            } else {
                $response[$dayName === 'tue' ? 'tues' : $dayName] = null;
            }
        }

        \Log::info('Teacher Lessons Retrieved:', [
            'teacher_id' => $teacher->id,
            'total_lessons' => $lessons->flatten()->count(),
            'days_with_lessons' => $lessons->keys()->toArray()
        ]);

        return response()->json([
            'lessons' => $response
        ]);
    }

    public function timeTable(Group $group)
    {
        $timeTable = $group->timeTable;
        return response()->json([
            'timeTable' => $timeTable->load(['days' => [
                'lessens' => function ($query) {
                    $query->orderBy('start_time')
                        ->with(['classRome', 'module', 'teacher']);
                }
            ]]),
        ]);
    }

    public function validClasses(Request $request, Group $group)
    {


        $department = $group->section->year->department;
        $classRomes = $department->classRomes->load('lessens.day');

        $validClasses = [];
        foreach ($classRomes as $classRome) {
            if (!$classRome->isBusy($request->start_time, $request->end_time, $request->day)) {
                $validClasses[] = $classRome;
            }
        }
        return response()->json($validClasses);
    }

    public function reserveClassRome(Request $request, Group $group)
    {
        $request->validate([
            'class_rome_id' => 'required|exists:class_romes,id',
            'day_id' => 'required|exists:days,id',
            'module_id' => 'required|exists:modules,id',
            'teacher_id' => 'required|exists:teachers,id',
            'start_time' => 'required|date_format:H:i:s',
            'end_time' => 'required|date_format:H:i:s',
            'type' => 'required|in:td,tp,course',
        ]);


        $timeTable = $group->timeTable;
        $day = $timeTable->days->where('id', $request->day_id)->first();
        // return response()->json($day);
        $lessen = $day->lessens()->create([
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'type' => $request->type,
            'day_id' => $request->day_id,
            'module_id' => $request->module_id,
            'teacher_id' => $request->teacher_id,
            'class_rome_id' => $request->class_rome_id,
        ]);
        $lessen->classRome()->associate($request->class_rome_id);
        $lessen->module()->associate($request->module_id);
        $lessen->teacher()->associate($request->teacher_id);
        $lessen->save();

        return response()->json($lessen->load('classRome', 'module', 'teacher', 'day'));
    }

    public function deleteLessen(Lessen $lessen)
    {
        $lessen->delete();
        return response()->json(['message' => 'Lessen deleted successfully']);
    }

    public function days(Group $group)
    {
        $days = $group->timeTable->days;
        return response()->json($days);
    }
}
