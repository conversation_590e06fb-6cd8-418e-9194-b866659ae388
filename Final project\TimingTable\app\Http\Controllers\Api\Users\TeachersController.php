<?php

namespace App\Http\Controllers\Api\Users;

use App\Http\Controllers\Controller;
use App\Models\Api\Main\TimeTable;
use App\Models\Api\Users\Teacher;
use Illuminate\Http\Request;

class TeachersController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $teachers = Teacher::with(['key.user' , 'baladiya.wilaya'])
            ->when($request->has('search'), function ($query) use ($request) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', '%' . $search . '%')
                      ->orWhere('last', 'like', '%' . $search . '%')
                      ->orWhere('username', 'like', '%' . $search . '%');
                });
            })
            ->orderBy('created_at', 'desc')
            ->paginate(6);

        return response()->json($teachers);
    }

    public function timeTable(Teacher $teacher)
    {
        $timeTable = TimeTable::with(['days' => [
            'lessens' => function($query) use ($teacher) {
                $query->where('teacher_id', $teacher->id)
                      ->orderBy('start_time')
                      ->with(['classRome', 'module']);
            }
        ]])->get();
        return response()->json($timeTable);
    }

    public function store(Request $request)
    {
        \Log::info('Teacher creation request:', $request->all());

        $validated = $request->validate([
            'username' => 'required|string|max:255|unique:teachers,username',
            'name' => 'required|string|max:255',
            'last' => 'required|string|max:255',
            'date_of_birth' => 'required|date',
            'grade' => 'nullable|string|max:255',
            'research_field' => 'nullable|string|max:255',
            'baladiya_id' => 'required|exists:baladiyas,id',
        ]);

        // Create the teacher with all validated fields
        $teacher = Teacher::create([
            'username' => $validated['username'],
            'name' => $validated['name'],
            'last' => $validated['last'],
            'date_of_birth' => $validated['date_of_birth'],
            'grade' => $validated['grade'],
            'research_field' => $validated['research_field'],
            'baladiya_id' => $validated['baladiya_id'],
        ]);

        \Log::info('Teacher created successfully:', ['teacher_id' => $teacher->id]);

        return response()->json([
            'message' => 'Teacher created successfully',
            'teacher' => $teacher->load(['baladiya.wilaya', 'key.user'])
        ], 201);
    }

    public function createKey(Teacher $teacher)
    {
        $teacher->key()->create([
            'value' => str()->random(10),
        ]);

        return response()->json([
            'message' => 'Key created successfully',
            'key' => $teacher->key->value,
        ], 201);
    }


    /**
     * Display the specified resource.
     */
    public function show(Teacher $teacher)
    {
        return response()->json([
            'message' => 'Teacher fetched successfully',
            'teacher' => $teacher->load('key.user')
        ], 200);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Teacher $teacher)
    {
        $validated = $request->validate([
            'username' => 'required|string|max:255|unique:teachers,username,' . $teacher->id,
            'name' => 'required|string|max:255',
            'last' => 'required|string|max:255',
            'date_of_birth' => 'required|date',
            'grade' => 'nullable|string|max:255',
            'research_field' => 'nullable|string|max:255',
            'baladiya_id' => 'required|exists:baladiyas,id',
        ]);

        $teacher->update($validated);

        return response()->json([
            'message' => 'Teacher updated successfully',
            'teacher' => $teacher->load(['baladiya.wilaya', 'key.user'])
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Teacher $teacher)
    {
        $teacher->key()->delete();
        $teacher->delete();
        return response()->json([
            'message' => 'Teacher deleted successfully'
        ], 200);
    }
}
