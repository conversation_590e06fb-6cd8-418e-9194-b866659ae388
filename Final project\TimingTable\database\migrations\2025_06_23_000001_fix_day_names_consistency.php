<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Api\Main\Day;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Fix day names to use consistent short format
        $dayMappings = [
            'Monday' => 'mon',
            'Tuesday' => 'tue',
            'Wednesday' => 'wed',
            'Thursday' => 'thu',
            'Friday' => 'fri',
            'Saturday' => 'sat',
            'Sunday' => 'sun',
        ];

        foreach ($dayMappings as $oldName => $newName) {
            Day::where('name', $oldName)->update(['name' => $newName]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reverse the day name changes
        $dayMappings = [
            'mon' => 'Monday',
            'tue' => 'Tuesday',
            'wed' => 'Wednesday',
            'thu' => 'Thursday',
            'fri' => 'Friday',
            'sat' => 'Saturday',
            'sun' => 'Sunday',
        ];

        foreach ($dayMappings as $oldName => $newName) {
            Day::where('name', $oldName)->update(['name' => $newName]);
        }
    }
};
