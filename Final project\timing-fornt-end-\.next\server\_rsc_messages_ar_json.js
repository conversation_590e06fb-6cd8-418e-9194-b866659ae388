"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_messages_ar_json";
exports.ids = ["_rsc_messages_ar_json"];
exports.modules = {

/***/ "(rsc)/./messages/ar.json":
/*!**************************!*\
  !*** ./messages/ar.json ***!
  \**************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"HomePage":{"title":"مرحبا بالعالم!","description":"وصف صفحة الهبوط","UpBar":{"login":"تسجيل الدخول","register":"إنشاء حساب","logout":"تسجيل الخروج","dashboard":"لوحة التحكم","home":"الرئيسية"}},"Dashborad":{"UpBar":{"Leave":"Leave","Home":"Home"}}}');

/***/ })

};
;