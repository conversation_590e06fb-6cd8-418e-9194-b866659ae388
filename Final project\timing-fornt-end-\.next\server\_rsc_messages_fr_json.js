"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_messages_fr_json";
exports.ids = ["_rsc_messages_fr_json"];
exports.modules = {

/***/ "(rsc)/./messages/fr.json":
/*!**************************!*\
  !*** ./messages/fr.json ***!
  \**************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"HomePage":{"title":"Bonjour le monde !","description":"Description de la page d\'accueil","UpBar":{"login":"Connexion","register":"S\'inscrire","logout":"Se déconnecter","dashboard":"Tableau de bord","home":"Accueil"}},"Dashborad":{"UpBar":{"Leave":"Leave","Home":"Home"}}}');

/***/ })

};
;