"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/intl-messageformat";
exports.ids = ["vendor-chunks/intl-messageformat"];
exports.modules = {

/***/ "(rsc)/./node_modules/intl-messageformat/lib/src/core.js":
/*!*********************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/core.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntlMessageFormat: () => (/* binding */ IntlMessageFormat)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @formatjs/fast-memoize */ \"(rsc)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n/* harmony import */ var _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @formatjs/icu-messageformat-parser */ \"(rsc)/./node_modules/@formatjs/icu-messageformat-parser/lib/index.js\");\n/* harmony import */ var _formatters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./formatters */ \"(rsc)/./node_modules/intl-messageformat/lib/src/formatters.js\");\n/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/\n\n\n\n\n// -- MessageFormat --------------------------------------------------------\nfunction mergeConfig(c1, c2) {\n    if (!c2) {\n        return c1;\n    }\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, (c1 || {})), (c2 || {})), Object.keys(c1).reduce(function (all, k) {\n        all[k] = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, c1[k]), (c2[k] || {}));\n        return all;\n    }, {}));\n}\nfunction mergeConfigs(defaultConfig, configs) {\n    if (!configs) {\n        return defaultConfig;\n    }\n    return Object.keys(defaultConfig).reduce(function (all, k) {\n        all[k] = mergeConfig(defaultConfig[k], configs[k]);\n        return all;\n    }, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, defaultConfig));\n}\nfunction createFastMemoizeCache(store) {\n    return {\n        create: function () {\n            return {\n                get: function (key) {\n                    return store[key];\n                },\n                set: function (key, value) {\n                    store[key] = value;\n                },\n            };\n        },\n    };\n}\nfunction createDefaultFormatters(cache) {\n    if (cache === void 0) { cache = {\n        number: {},\n        dateTime: {},\n        pluralRules: {},\n    }; }\n    return {\n        getNumberFormat: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.NumberFormat).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.number),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic,\n        }),\n        getDateTimeFormat: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.DateTimeFormat).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.dateTime),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic,\n        }),\n        getPluralRules: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.PluralRules).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.pluralRules),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic,\n        }),\n    };\n}\nvar IntlMessageFormat = /** @class */ (function () {\n    function IntlMessageFormat(message, locales, overrideFormats, opts) {\n        if (locales === void 0) { locales = IntlMessageFormat.defaultLocale; }\n        var _this = this;\n        this.formatterCache = {\n            number: {},\n            dateTime: {},\n            pluralRules: {},\n        };\n        this.format = function (values) {\n            var parts = _this.formatToParts(values);\n            // Hot path for straight simple msg translations\n            if (parts.length === 1) {\n                return parts[0].value;\n            }\n            var result = parts.reduce(function (all, part) {\n                if (!all.length ||\n                    part.type !== _formatters__WEBPACK_IMPORTED_MODULE_3__.PART_TYPE.literal ||\n                    typeof all[all.length - 1] !== 'string') {\n                    all.push(part.value);\n                }\n                else {\n                    all[all.length - 1] += part.value;\n                }\n                return all;\n            }, []);\n            if (result.length <= 1) {\n                return result[0] || '';\n            }\n            return result;\n        };\n        this.formatToParts = function (values) {\n            return (0,_formatters__WEBPACK_IMPORTED_MODULE_3__.formatToParts)(_this.ast, _this.locales, _this.formatters, _this.formats, values, undefined, _this.message);\n        };\n        this.resolvedOptions = function () {\n            var _a;\n            return ({\n                locale: ((_a = _this.resolvedLocale) === null || _a === void 0 ? void 0 : _a.toString()) ||\n                    Intl.NumberFormat.supportedLocalesOf(_this.locales)[0],\n            });\n        };\n        this.getAst = function () { return _this.ast; };\n        // Defined first because it's used to build the format pattern.\n        this.locales = locales;\n        this.resolvedLocale = IntlMessageFormat.resolveLocale(locales);\n        if (typeof message === 'string') {\n            this.message = message;\n            if (!IntlMessageFormat.__parse) {\n                throw new TypeError('IntlMessageFormat.__parse must be set to process `message` of type `string`');\n            }\n            var _a = opts || {}, formatters = _a.formatters, parseOpts = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__rest)(_a, [\"formatters\"]);\n            // Parse string messages into an AST.\n            this.ast = IntlMessageFormat.__parse(message, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, parseOpts), { locale: this.resolvedLocale }));\n        }\n        else {\n            this.ast = message;\n        }\n        if (!Array.isArray(this.ast)) {\n            throw new TypeError('A message must be provided as a String or AST.');\n        }\n        // Creates a new object with the specified `formats` merged with the default\n        // formats.\n        this.formats = mergeConfigs(IntlMessageFormat.formats, overrideFormats);\n        this.formatters =\n            (opts && opts.formatters) || createDefaultFormatters(this.formatterCache);\n    }\n    Object.defineProperty(IntlMessageFormat, \"defaultLocale\", {\n        get: function () {\n            if (!IntlMessageFormat.memoizedDefaultLocale) {\n                IntlMessageFormat.memoizedDefaultLocale =\n                    new Intl.NumberFormat().resolvedOptions().locale;\n            }\n            return IntlMessageFormat.memoizedDefaultLocale;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    IntlMessageFormat.memoizedDefaultLocale = null;\n    IntlMessageFormat.resolveLocale = function (locales) {\n        if (typeof Intl.Locale === 'undefined') {\n            return;\n        }\n        var supportedLocales = Intl.NumberFormat.supportedLocalesOf(locales);\n        if (supportedLocales.length > 0) {\n            return new Intl.Locale(supportedLocales[0]);\n        }\n        return new Intl.Locale(typeof locales === 'string' ? locales : locales[0]);\n    };\n    IntlMessageFormat.__parse = _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_1__.parse;\n    // Default format options used as the prototype of the `formats` provided to the\n    // constructor. These are used when constructing the internal Intl.NumberFormat\n    // and Intl.DateTimeFormat instances.\n    IntlMessageFormat.formats = {\n        number: {\n            integer: {\n                maximumFractionDigits: 0,\n            },\n            currency: {\n                style: 'currency',\n            },\n            percent: {\n                style: 'percent',\n            },\n        },\n        date: {\n            short: {\n                month: 'numeric',\n                day: 'numeric',\n                year: '2-digit',\n            },\n            medium: {\n                month: 'short',\n                day: 'numeric',\n                year: 'numeric',\n            },\n            long: {\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric',\n            },\n            full: {\n                weekday: 'long',\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric',\n            },\n        },\n        time: {\n            short: {\n                hour: 'numeric',\n                minute: 'numeric',\n            },\n            medium: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n            },\n            long: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short',\n            },\n            full: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short',\n            },\n        },\n    };\n    return IntlMessageFormat;\n}());\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/intl-messageformat/lib/src/core.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/intl-messageformat/lib/src/error.js":
/*!**********************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/error.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorCode: () => (/* binding */ ErrorCode),\n/* harmony export */   FormatError: () => (/* binding */ FormatError),\n/* harmony export */   InvalidValueError: () => (/* binding */ InvalidValueError),\n/* harmony export */   InvalidValueTypeError: () => (/* binding */ InvalidValueTypeError),\n/* harmony export */   MissingValueError: () => (/* binding */ MissingValueError)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/tslib/tslib.es6.mjs\");\n\nvar ErrorCode;\n(function (ErrorCode) {\n    // When we have a placeholder but no value to format\n    ErrorCode[\"MISSING_VALUE\"] = \"MISSING_VALUE\";\n    // When value supplied is invalid\n    ErrorCode[\"INVALID_VALUE\"] = \"INVALID_VALUE\";\n    // When we need specific Intl API but it's not available\n    ErrorCode[\"MISSING_INTL_API\"] = \"MISSING_INTL_API\";\n})(ErrorCode || (ErrorCode = {}));\nvar FormatError = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(FormatError, _super);\n    function FormatError(msg, code, originalMessage) {\n        var _this = _super.call(this, msg) || this;\n        _this.code = code;\n        _this.originalMessage = originalMessage;\n        return _this;\n    }\n    FormatError.prototype.toString = function () {\n        return \"[formatjs Error: \".concat(this.code, \"] \").concat(this.message);\n    };\n    return FormatError;\n}(Error));\n\nvar InvalidValueError = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(InvalidValueError, _super);\n    function InvalidValueError(variableId, value, options, originalMessage) {\n        return _super.call(this, \"Invalid values for \\\"\".concat(variableId, \"\\\": \\\"\").concat(value, \"\\\". Options are \\\"\").concat(Object.keys(options).join('\", \"'), \"\\\"\"), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueError;\n}(FormatError));\n\nvar InvalidValueTypeError = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(InvalidValueTypeError, _super);\n    function InvalidValueTypeError(value, type, originalMessage) {\n        return _super.call(this, \"Value for \\\"\".concat(value, \"\\\" must be of type \").concat(type), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueTypeError;\n}(FormatError));\n\nvar MissingValueError = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(MissingValueError, _super);\n    function MissingValueError(variableId, originalMessage) {\n        return _super.call(this, \"The intl string context variable \\\"\".concat(variableId, \"\\\" was not provided to the string \\\"\").concat(originalMessage, \"\\\"\"), ErrorCode.MISSING_VALUE, originalMessage) || this;\n    }\n    return MissingValueError;\n}(FormatError));\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/intl-messageformat/lib/src/error.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/intl-messageformat/lib/src/formatters.js":
/*!***************************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/formatters.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PART_TYPE: () => (/* binding */ PART_TYPE),\n/* harmony export */   formatToParts: () => (/* binding */ formatToParts),\n/* harmony export */   isFormatXMLElementFn: () => (/* binding */ isFormatXMLElementFn)\n/* harmony export */ });\n/* harmony import */ var _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @formatjs/icu-messageformat-parser */ \"(rsc)/./node_modules/@formatjs/icu-messageformat-parser/lib/index.js\");\n/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./error */ \"(rsc)/./node_modules/intl-messageformat/lib/src/error.js\");\n\n\nvar PART_TYPE;\n(function (PART_TYPE) {\n    PART_TYPE[PART_TYPE[\"literal\"] = 0] = \"literal\";\n    PART_TYPE[PART_TYPE[\"object\"] = 1] = \"object\";\n})(PART_TYPE || (PART_TYPE = {}));\nfunction mergeLiteral(parts) {\n    if (parts.length < 2) {\n        return parts;\n    }\n    return parts.reduce(function (all, part) {\n        var lastPart = all[all.length - 1];\n        if (!lastPart ||\n            lastPart.type !== PART_TYPE.literal ||\n            part.type !== PART_TYPE.literal) {\n            all.push(part);\n        }\n        else {\n            lastPart.value += part.value;\n        }\n        return all;\n    }, []);\n}\nfunction isFormatXMLElementFn(el) {\n    return typeof el === 'function';\n}\n// TODO(skeleton): add skeleton support\nfunction formatToParts(els, locales, formatters, formats, values, currentPluralValue, \n// For debugging\noriginalMessage) {\n    // Hot path for straight simple msg translations\n    if (els.length === 1 && (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isLiteralElement)(els[0])) {\n        return [\n            {\n                type: PART_TYPE.literal,\n                value: els[0].value,\n            },\n        ];\n    }\n    var result = [];\n    for (var _i = 0, els_1 = els; _i < els_1.length; _i++) {\n        var el = els_1[_i];\n        // Exit early for string parts.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isLiteralElement)(el)) {\n            result.push({\n                type: PART_TYPE.literal,\n                value: el.value,\n            });\n            continue;\n        }\n        // TODO: should this part be literal type?\n        // Replace `#` in plural rules with the actual numeric value.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isPoundElement)(el)) {\n            if (typeof currentPluralValue === 'number') {\n                result.push({\n                    type: PART_TYPE.literal,\n                    value: formatters.getNumberFormat(locales).format(currentPluralValue),\n                });\n            }\n            continue;\n        }\n        var varName = el.value;\n        // Enforce that all required values are provided by the caller.\n        if (!(values && varName in values)) {\n            throw new _error__WEBPACK_IMPORTED_MODULE_1__.MissingValueError(varName, originalMessage);\n        }\n        var value = values[varName];\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isArgumentElement)(el)) {\n            if (!value || typeof value === 'string' || typeof value === 'number') {\n                value =\n                    typeof value === 'string' || typeof value === 'number'\n                        ? String(value)\n                        : '';\n            }\n            result.push({\n                type: typeof value === 'string' ? PART_TYPE.literal : PART_TYPE.object,\n                value: value,\n            });\n            continue;\n        }\n        // Recursively format plural and select parts' option — which can be a\n        // nested pattern structure. The choosing of the option to use is\n        // abstracted-by and delegated-to the part helper object.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateElement)(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.date[el.style]\n                : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateTimeSkeleton)(el.style)\n                    ? el.style.parsedOptions\n                    : undefined;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getDateTimeFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isTimeElement)(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.time[el.style]\n                : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateTimeSkeleton)(el.style)\n                    ? el.style.parsedOptions\n                    : formats.time.medium;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getDateTimeFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isNumberElement)(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.number[el.style]\n                : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isNumberSkeleton)(el.style)\n                    ? el.style.parsedOptions\n                    : undefined;\n            if (style && style.scale) {\n                value =\n                    value *\n                        (style.scale || 1);\n            }\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getNumberFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isTagElement)(el)) {\n            var children = el.children, value_1 = el.value;\n            var formatFn = values[value_1];\n            if (!isFormatXMLElementFn(formatFn)) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueTypeError(value_1, 'function', originalMessage);\n            }\n            var parts = formatToParts(children, locales, formatters, formats, values, currentPluralValue);\n            var chunks = formatFn(parts.map(function (p) { return p.value; }));\n            if (!Array.isArray(chunks)) {\n                chunks = [chunks];\n            }\n            result.push.apply(result, chunks.map(function (c) {\n                return {\n                    type: typeof c === 'string' ? PART_TYPE.literal : PART_TYPE.object,\n                    value: c,\n                };\n            }));\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isSelectElement)(el)) {\n            var opt = el.options[value] || el.options.other;\n            if (!opt) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values));\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isPluralElement)(el)) {\n            var opt = el.options[\"=\".concat(value)];\n            if (!opt) {\n                if (!Intl.PluralRules) {\n                    throw new _error__WEBPACK_IMPORTED_MODULE_1__.FormatError(\"Intl.PluralRules is not available in this environment.\\nTry polyfilling it using \\\"@formatjs/intl-pluralrules\\\"\\n\", _error__WEBPACK_IMPORTED_MODULE_1__.ErrorCode.MISSING_INTL_API, originalMessage);\n                }\n                var rule = formatters\n                    .getPluralRules(locales, { type: el.pluralType })\n                    .select(value - (el.offset || 0));\n                opt = el.options[rule] || el.options.other;\n            }\n            if (!opt) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values, value - (el.offset || 0)));\n            continue;\n        }\n    }\n    return mergeLiteral(result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/intl-messageformat/lib/src/formatters.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/intl-messageformat/lib/src/core.js":
/*!*********************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/core.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntlMessageFormat: () => (/* binding */ IntlMessageFormat)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n/* harmony import */ var _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @formatjs/icu-messageformat-parser */ \"(ssr)/./node_modules/@formatjs/icu-messageformat-parser/lib/index.js\");\n/* harmony import */ var _formatters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./formatters */ \"(ssr)/./node_modules/intl-messageformat/lib/src/formatters.js\");\n/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/\n\n\n\n\n// -- MessageFormat --------------------------------------------------------\nfunction mergeConfig(c1, c2) {\n    if (!c2) {\n        return c1;\n    }\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, (c1 || {})), (c2 || {})), Object.keys(c1).reduce(function (all, k) {\n        all[k] = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, c1[k]), (c2[k] || {}));\n        return all;\n    }, {}));\n}\nfunction mergeConfigs(defaultConfig, configs) {\n    if (!configs) {\n        return defaultConfig;\n    }\n    return Object.keys(defaultConfig).reduce(function (all, k) {\n        all[k] = mergeConfig(defaultConfig[k], configs[k]);\n        return all;\n    }, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, defaultConfig));\n}\nfunction createFastMemoizeCache(store) {\n    return {\n        create: function () {\n            return {\n                get: function (key) {\n                    return store[key];\n                },\n                set: function (key, value) {\n                    store[key] = value;\n                },\n            };\n        },\n    };\n}\nfunction createDefaultFormatters(cache) {\n    if (cache === void 0) { cache = {\n        number: {},\n        dateTime: {},\n        pluralRules: {},\n    }; }\n    return {\n        getNumberFormat: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.NumberFormat).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.number),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic,\n        }),\n        getDateTimeFormat: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.DateTimeFormat).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.dateTime),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic,\n        }),\n        getPluralRules: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.PluralRules).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.pluralRules),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic,\n        }),\n    };\n}\nvar IntlMessageFormat = /** @class */ (function () {\n    function IntlMessageFormat(message, locales, overrideFormats, opts) {\n        if (locales === void 0) { locales = IntlMessageFormat.defaultLocale; }\n        var _this = this;\n        this.formatterCache = {\n            number: {},\n            dateTime: {},\n            pluralRules: {},\n        };\n        this.format = function (values) {\n            var parts = _this.formatToParts(values);\n            // Hot path for straight simple msg translations\n            if (parts.length === 1) {\n                return parts[0].value;\n            }\n            var result = parts.reduce(function (all, part) {\n                if (!all.length ||\n                    part.type !== _formatters__WEBPACK_IMPORTED_MODULE_3__.PART_TYPE.literal ||\n                    typeof all[all.length - 1] !== 'string') {\n                    all.push(part.value);\n                }\n                else {\n                    all[all.length - 1] += part.value;\n                }\n                return all;\n            }, []);\n            if (result.length <= 1) {\n                return result[0] || '';\n            }\n            return result;\n        };\n        this.formatToParts = function (values) {\n            return (0,_formatters__WEBPACK_IMPORTED_MODULE_3__.formatToParts)(_this.ast, _this.locales, _this.formatters, _this.formats, values, undefined, _this.message);\n        };\n        this.resolvedOptions = function () {\n            var _a;\n            return ({\n                locale: ((_a = _this.resolvedLocale) === null || _a === void 0 ? void 0 : _a.toString()) ||\n                    Intl.NumberFormat.supportedLocalesOf(_this.locales)[0],\n            });\n        };\n        this.getAst = function () { return _this.ast; };\n        // Defined first because it's used to build the format pattern.\n        this.locales = locales;\n        this.resolvedLocale = IntlMessageFormat.resolveLocale(locales);\n        if (typeof message === 'string') {\n            this.message = message;\n            if (!IntlMessageFormat.__parse) {\n                throw new TypeError('IntlMessageFormat.__parse must be set to process `message` of type `string`');\n            }\n            var _a = opts || {}, formatters = _a.formatters, parseOpts = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__rest)(_a, [\"formatters\"]);\n            // Parse string messages into an AST.\n            this.ast = IntlMessageFormat.__parse(message, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, parseOpts), { locale: this.resolvedLocale }));\n        }\n        else {\n            this.ast = message;\n        }\n        if (!Array.isArray(this.ast)) {\n            throw new TypeError('A message must be provided as a String or AST.');\n        }\n        // Creates a new object with the specified `formats` merged with the default\n        // formats.\n        this.formats = mergeConfigs(IntlMessageFormat.formats, overrideFormats);\n        this.formatters =\n            (opts && opts.formatters) || createDefaultFormatters(this.formatterCache);\n    }\n    Object.defineProperty(IntlMessageFormat, \"defaultLocale\", {\n        get: function () {\n            if (!IntlMessageFormat.memoizedDefaultLocale) {\n                IntlMessageFormat.memoizedDefaultLocale =\n                    new Intl.NumberFormat().resolvedOptions().locale;\n            }\n            return IntlMessageFormat.memoizedDefaultLocale;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    IntlMessageFormat.memoizedDefaultLocale = null;\n    IntlMessageFormat.resolveLocale = function (locales) {\n        if (typeof Intl.Locale === 'undefined') {\n            return;\n        }\n        var supportedLocales = Intl.NumberFormat.supportedLocalesOf(locales);\n        if (supportedLocales.length > 0) {\n            return new Intl.Locale(supportedLocales[0]);\n        }\n        return new Intl.Locale(typeof locales === 'string' ? locales : locales[0]);\n    };\n    IntlMessageFormat.__parse = _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_1__.parse;\n    // Default format options used as the prototype of the `formats` provided to the\n    // constructor. These are used when constructing the internal Intl.NumberFormat\n    // and Intl.DateTimeFormat instances.\n    IntlMessageFormat.formats = {\n        number: {\n            integer: {\n                maximumFractionDigits: 0,\n            },\n            currency: {\n                style: 'currency',\n            },\n            percent: {\n                style: 'percent',\n            },\n        },\n        date: {\n            short: {\n                month: 'numeric',\n                day: 'numeric',\n                year: '2-digit',\n            },\n            medium: {\n                month: 'short',\n                day: 'numeric',\n                year: 'numeric',\n            },\n            long: {\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric',\n            },\n            full: {\n                weekday: 'long',\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric',\n            },\n        },\n        time: {\n            short: {\n                hour: 'numeric',\n                minute: 'numeric',\n            },\n            medium: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n            },\n            long: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short',\n            },\n            full: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short',\n            },\n        },\n    };\n    return IntlMessageFormat;\n}());\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/intl-messageformat/lib/src/core.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/intl-messageformat/lib/src/error.js":
/*!**********************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/error.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorCode: () => (/* binding */ ErrorCode),\n/* harmony export */   FormatError: () => (/* binding */ FormatError),\n/* harmony export */   InvalidValueError: () => (/* binding */ InvalidValueError),\n/* harmony export */   InvalidValueTypeError: () => (/* binding */ InvalidValueTypeError),\n/* harmony export */   MissingValueError: () => (/* binding */ MissingValueError)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\n\nvar ErrorCode;\n(function (ErrorCode) {\n    // When we have a placeholder but no value to format\n    ErrorCode[\"MISSING_VALUE\"] = \"MISSING_VALUE\";\n    // When value supplied is invalid\n    ErrorCode[\"INVALID_VALUE\"] = \"INVALID_VALUE\";\n    // When we need specific Intl API but it's not available\n    ErrorCode[\"MISSING_INTL_API\"] = \"MISSING_INTL_API\";\n})(ErrorCode || (ErrorCode = {}));\nvar FormatError = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(FormatError, _super);\n    function FormatError(msg, code, originalMessage) {\n        var _this = _super.call(this, msg) || this;\n        _this.code = code;\n        _this.originalMessage = originalMessage;\n        return _this;\n    }\n    FormatError.prototype.toString = function () {\n        return \"[formatjs Error: \".concat(this.code, \"] \").concat(this.message);\n    };\n    return FormatError;\n}(Error));\n\nvar InvalidValueError = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(InvalidValueError, _super);\n    function InvalidValueError(variableId, value, options, originalMessage) {\n        return _super.call(this, \"Invalid values for \\\"\".concat(variableId, \"\\\": \\\"\").concat(value, \"\\\". Options are \\\"\").concat(Object.keys(options).join('\", \"'), \"\\\"\"), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueError;\n}(FormatError));\n\nvar InvalidValueTypeError = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(InvalidValueTypeError, _super);\n    function InvalidValueTypeError(value, type, originalMessage) {\n        return _super.call(this, \"Value for \\\"\".concat(value, \"\\\" must be of type \").concat(type), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueTypeError;\n}(FormatError));\n\nvar MissingValueError = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(MissingValueError, _super);\n    function MissingValueError(variableId, originalMessage) {\n        return _super.call(this, \"The intl string context variable \\\"\".concat(variableId, \"\\\" was not provided to the string \\\"\").concat(originalMessage, \"\\\"\"), ErrorCode.MISSING_VALUE, originalMessage) || this;\n    }\n    return MissingValueError;\n}(FormatError));\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaW50bC1tZXNzYWdlZm9ybWF0L2xpYi9zcmMvZXJyb3IuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQWtDO0FBQzNCO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLDhCQUE4QjtBQUMvQjtBQUNBLElBQUksZ0RBQVM7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDc0I7QUFDdkI7QUFDQSxJQUFJLGdEQUFTO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQzRCO0FBQzdCO0FBQ0EsSUFBSSxnREFBUztBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNnQztBQUNqQztBQUNBLElBQUksZ0RBQVM7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDNEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccHJvXFxEZXNrdG9wXFxmaW4gcHJvaiBjb3BpZSAyXFxGaW5hbCBwcm9qZWN0XFx0aW1pbmctZm9ybnQtZW5kLVxcbm9kZV9tb2R1bGVzXFxpbnRsLW1lc3NhZ2Vmb3JtYXRcXGxpYlxcc3JjXFxlcnJvci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBfX2V4dGVuZHMgfSBmcm9tIFwidHNsaWJcIjtcbmV4cG9ydCB2YXIgRXJyb3JDb2RlO1xuKGZ1bmN0aW9uIChFcnJvckNvZGUpIHtcbiAgICAvLyBXaGVuIHdlIGhhdmUgYSBwbGFjZWhvbGRlciBidXQgbm8gdmFsdWUgdG8gZm9ybWF0XG4gICAgRXJyb3JDb2RlW1wiTUlTU0lOR19WQUxVRVwiXSA9IFwiTUlTU0lOR19WQUxVRVwiO1xuICAgIC8vIFdoZW4gdmFsdWUgc3VwcGxpZWQgaXMgaW52YWxpZFxuICAgIEVycm9yQ29kZVtcIklOVkFMSURfVkFMVUVcIl0gPSBcIklOVkFMSURfVkFMVUVcIjtcbiAgICAvLyBXaGVuIHdlIG5lZWQgc3BlY2lmaWMgSW50bCBBUEkgYnV0IGl0J3Mgbm90IGF2YWlsYWJsZVxuICAgIEVycm9yQ29kZVtcIk1JU1NJTkdfSU5UTF9BUElcIl0gPSBcIk1JU1NJTkdfSU5UTF9BUElcIjtcbn0pKEVycm9yQ29kZSB8fCAoRXJyb3JDb2RlID0ge30pKTtcbnZhciBGb3JtYXRFcnJvciA9IC8qKiBAY2xhc3MgKi8gKGZ1bmN0aW9uIChfc3VwZXIpIHtcbiAgICBfX2V4dGVuZHMoRm9ybWF0RXJyb3IsIF9zdXBlcik7XG4gICAgZnVuY3Rpb24gRm9ybWF0RXJyb3IobXNnLCBjb2RlLCBvcmlnaW5hbE1lc3NhZ2UpIHtcbiAgICAgICAgdmFyIF90aGlzID0gX3N1cGVyLmNhbGwodGhpcywgbXNnKSB8fCB0aGlzO1xuICAgICAgICBfdGhpcy5jb2RlID0gY29kZTtcbiAgICAgICAgX3RoaXMub3JpZ2luYWxNZXNzYWdlID0gb3JpZ2luYWxNZXNzYWdlO1xuICAgICAgICByZXR1cm4gX3RoaXM7XG4gICAgfVxuICAgIEZvcm1hdEVycm9yLnByb3RvdHlwZS50b1N0cmluZyA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIFwiW2Zvcm1hdGpzIEVycm9yOiBcIi5jb25jYXQodGhpcy5jb2RlLCBcIl0gXCIpLmNvbmNhdCh0aGlzLm1lc3NhZ2UpO1xuICAgIH07XG4gICAgcmV0dXJuIEZvcm1hdEVycm9yO1xufShFcnJvcikpO1xuZXhwb3J0IHsgRm9ybWF0RXJyb3IgfTtcbnZhciBJbnZhbGlkVmFsdWVFcnJvciA9IC8qKiBAY2xhc3MgKi8gKGZ1bmN0aW9uIChfc3VwZXIpIHtcbiAgICBfX2V4dGVuZHMoSW52YWxpZFZhbHVlRXJyb3IsIF9zdXBlcik7XG4gICAgZnVuY3Rpb24gSW52YWxpZFZhbHVlRXJyb3IodmFyaWFibGVJZCwgdmFsdWUsIG9wdGlvbnMsIG9yaWdpbmFsTWVzc2FnZSkge1xuICAgICAgICByZXR1cm4gX3N1cGVyLmNhbGwodGhpcywgXCJJbnZhbGlkIHZhbHVlcyBmb3IgXFxcIlwiLmNvbmNhdCh2YXJpYWJsZUlkLCBcIlxcXCI6IFxcXCJcIikuY29uY2F0KHZhbHVlLCBcIlxcXCIuIE9wdGlvbnMgYXJlIFxcXCJcIikuY29uY2F0KE9iamVjdC5rZXlzKG9wdGlvbnMpLmpvaW4oJ1wiLCBcIicpLCBcIlxcXCJcIiksIEVycm9yQ29kZS5JTlZBTElEX1ZBTFVFLCBvcmlnaW5hbE1lc3NhZ2UpIHx8IHRoaXM7XG4gICAgfVxuICAgIHJldHVybiBJbnZhbGlkVmFsdWVFcnJvcjtcbn0oRm9ybWF0RXJyb3IpKTtcbmV4cG9ydCB7IEludmFsaWRWYWx1ZUVycm9yIH07XG52YXIgSW52YWxpZFZhbHVlVHlwZUVycm9yID0gLyoqIEBjbGFzcyAqLyAoZnVuY3Rpb24gKF9zdXBlcikge1xuICAgIF9fZXh0ZW5kcyhJbnZhbGlkVmFsdWVUeXBlRXJyb3IsIF9zdXBlcik7XG4gICAgZnVuY3Rpb24gSW52YWxpZFZhbHVlVHlwZUVycm9yKHZhbHVlLCB0eXBlLCBvcmlnaW5hbE1lc3NhZ2UpIHtcbiAgICAgICAgcmV0dXJuIF9zdXBlci5jYWxsKHRoaXMsIFwiVmFsdWUgZm9yIFxcXCJcIi5jb25jYXQodmFsdWUsIFwiXFxcIiBtdXN0IGJlIG9mIHR5cGUgXCIpLmNvbmNhdCh0eXBlKSwgRXJyb3JDb2RlLklOVkFMSURfVkFMVUUsIG9yaWdpbmFsTWVzc2FnZSkgfHwgdGhpcztcbiAgICB9XG4gICAgcmV0dXJuIEludmFsaWRWYWx1ZVR5cGVFcnJvcjtcbn0oRm9ybWF0RXJyb3IpKTtcbmV4cG9ydCB7IEludmFsaWRWYWx1ZVR5cGVFcnJvciB9O1xudmFyIE1pc3NpbmdWYWx1ZUVycm9yID0gLyoqIEBjbGFzcyAqLyAoZnVuY3Rpb24gKF9zdXBlcikge1xuICAgIF9fZXh0ZW5kcyhNaXNzaW5nVmFsdWVFcnJvciwgX3N1cGVyKTtcbiAgICBmdW5jdGlvbiBNaXNzaW5nVmFsdWVFcnJvcih2YXJpYWJsZUlkLCBvcmlnaW5hbE1lc3NhZ2UpIHtcbiAgICAgICAgcmV0dXJuIF9zdXBlci5jYWxsKHRoaXMsIFwiVGhlIGludGwgc3RyaW5nIGNvbnRleHQgdmFyaWFibGUgXFxcIlwiLmNvbmNhdCh2YXJpYWJsZUlkLCBcIlxcXCIgd2FzIG5vdCBwcm92aWRlZCB0byB0aGUgc3RyaW5nIFxcXCJcIikuY29uY2F0KG9yaWdpbmFsTWVzc2FnZSwgXCJcXFwiXCIpLCBFcnJvckNvZGUuTUlTU0lOR19WQUxVRSwgb3JpZ2luYWxNZXNzYWdlKSB8fCB0aGlzO1xuICAgIH1cbiAgICByZXR1cm4gTWlzc2luZ1ZhbHVlRXJyb3I7XG59KEZvcm1hdEVycm9yKSk7XG5leHBvcnQgeyBNaXNzaW5nVmFsdWVFcnJvciB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/intl-messageformat/lib/src/error.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/intl-messageformat/lib/src/formatters.js":
/*!***************************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/formatters.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PART_TYPE: () => (/* binding */ PART_TYPE),\n/* harmony export */   formatToParts: () => (/* binding */ formatToParts),\n/* harmony export */   isFormatXMLElementFn: () => (/* binding */ isFormatXMLElementFn)\n/* harmony export */ });\n/* harmony import */ var _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @formatjs/icu-messageformat-parser */ \"(ssr)/./node_modules/@formatjs/icu-messageformat-parser/lib/index.js\");\n/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./error */ \"(ssr)/./node_modules/intl-messageformat/lib/src/error.js\");\n\n\nvar PART_TYPE;\n(function (PART_TYPE) {\n    PART_TYPE[PART_TYPE[\"literal\"] = 0] = \"literal\";\n    PART_TYPE[PART_TYPE[\"object\"] = 1] = \"object\";\n})(PART_TYPE || (PART_TYPE = {}));\nfunction mergeLiteral(parts) {\n    if (parts.length < 2) {\n        return parts;\n    }\n    return parts.reduce(function (all, part) {\n        var lastPart = all[all.length - 1];\n        if (!lastPart ||\n            lastPart.type !== PART_TYPE.literal ||\n            part.type !== PART_TYPE.literal) {\n            all.push(part);\n        }\n        else {\n            lastPart.value += part.value;\n        }\n        return all;\n    }, []);\n}\nfunction isFormatXMLElementFn(el) {\n    return typeof el === 'function';\n}\n// TODO(skeleton): add skeleton support\nfunction formatToParts(els, locales, formatters, formats, values, currentPluralValue, \n// For debugging\noriginalMessage) {\n    // Hot path for straight simple msg translations\n    if (els.length === 1 && (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isLiteralElement)(els[0])) {\n        return [\n            {\n                type: PART_TYPE.literal,\n                value: els[0].value,\n            },\n        ];\n    }\n    var result = [];\n    for (var _i = 0, els_1 = els; _i < els_1.length; _i++) {\n        var el = els_1[_i];\n        // Exit early for string parts.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isLiteralElement)(el)) {\n            result.push({\n                type: PART_TYPE.literal,\n                value: el.value,\n            });\n            continue;\n        }\n        // TODO: should this part be literal type?\n        // Replace `#` in plural rules with the actual numeric value.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isPoundElement)(el)) {\n            if (typeof currentPluralValue === 'number') {\n                result.push({\n                    type: PART_TYPE.literal,\n                    value: formatters.getNumberFormat(locales).format(currentPluralValue),\n                });\n            }\n            continue;\n        }\n        var varName = el.value;\n        // Enforce that all required values are provided by the caller.\n        if (!(values && varName in values)) {\n            throw new _error__WEBPACK_IMPORTED_MODULE_1__.MissingValueError(varName, originalMessage);\n        }\n        var value = values[varName];\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isArgumentElement)(el)) {\n            if (!value || typeof value === 'string' || typeof value === 'number') {\n                value =\n                    typeof value === 'string' || typeof value === 'number'\n                        ? String(value)\n                        : '';\n            }\n            result.push({\n                type: typeof value === 'string' ? PART_TYPE.literal : PART_TYPE.object,\n                value: value,\n            });\n            continue;\n        }\n        // Recursively format plural and select parts' option — which can be a\n        // nested pattern structure. The choosing of the option to use is\n        // abstracted-by and delegated-to the part helper object.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateElement)(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.date[el.style]\n                : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateTimeSkeleton)(el.style)\n                    ? el.style.parsedOptions\n                    : undefined;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getDateTimeFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isTimeElement)(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.time[el.style]\n                : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateTimeSkeleton)(el.style)\n                    ? el.style.parsedOptions\n                    : formats.time.medium;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getDateTimeFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isNumberElement)(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.number[el.style]\n                : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isNumberSkeleton)(el.style)\n                    ? el.style.parsedOptions\n                    : undefined;\n            if (style && style.scale) {\n                value =\n                    value *\n                        (style.scale || 1);\n            }\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getNumberFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isTagElement)(el)) {\n            var children = el.children, value_1 = el.value;\n            var formatFn = values[value_1];\n            if (!isFormatXMLElementFn(formatFn)) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueTypeError(value_1, 'function', originalMessage);\n            }\n            var parts = formatToParts(children, locales, formatters, formats, values, currentPluralValue);\n            var chunks = formatFn(parts.map(function (p) { return p.value; }));\n            if (!Array.isArray(chunks)) {\n                chunks = [chunks];\n            }\n            result.push.apply(result, chunks.map(function (c) {\n                return {\n                    type: typeof c === 'string' ? PART_TYPE.literal : PART_TYPE.object,\n                    value: c,\n                };\n            }));\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isSelectElement)(el)) {\n            var opt = el.options[value] || el.options.other;\n            if (!opt) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values));\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isPluralElement)(el)) {\n            var opt = el.options[\"=\".concat(value)];\n            if (!opt) {\n                if (!Intl.PluralRules) {\n                    throw new _error__WEBPACK_IMPORTED_MODULE_1__.FormatError(\"Intl.PluralRules is not available in this environment.\\nTry polyfilling it using \\\"@formatjs/intl-pluralrules\\\"\\n\", _error__WEBPACK_IMPORTED_MODULE_1__.ErrorCode.MISSING_INTL_API, originalMessage);\n                }\n                var rule = formatters\n                    .getPluralRules(locales, { type: el.pluralType })\n                    .select(value - (el.offset || 0));\n                opt = el.options[rule] || el.options.other;\n            }\n            if (!opt) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values, value - (el.offset || 0)));\n            continue;\n        }\n    }\n    return mergeLiteral(result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/intl-messageformat/lib/src/formatters.js\n");

/***/ })

};
;