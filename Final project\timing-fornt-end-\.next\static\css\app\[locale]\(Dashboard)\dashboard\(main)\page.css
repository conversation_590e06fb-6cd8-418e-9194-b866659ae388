/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/globals.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
/*! tailwindcss v4.1.6 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
      "Courier New", monospace;
    --color-red-50: oklch(97.1% 0.013 17.38);
    --color-red-200: oklch(88.5% 0.062 18.334);
    --color-red-300: oklch(80.8% 0.114 19.571);
    --color-red-400: oklch(70.4% 0.191 22.216);
    --color-red-500: oklch(63.7% 0.237 25.331);
    --color-red-600: oklch(57.7% 0.245 27.325);
    --color-red-700: oklch(50.5% 0.213 27.518);
    --color-red-800: oklch(44.4% 0.177 26.899);
    --color-red-900: oklch(39.6% 0.141 25.723);
    --color-green-50: oklch(98.2% 0.018 155.826);
    --color-green-100: oklch(96.2% 0.044 156.743);
    --color-green-200: oklch(92.5% 0.084 155.995);
    --color-green-300: oklch(87.1% 0.15 154.449);
    --color-green-400: oklch(79.2% 0.209 151.711);
    --color-green-500: oklch(72.3% 0.219 149.579);
    --color-green-600: oklch(62.7% 0.194 149.214);
    --color-green-700: oklch(52.7% 0.154 150.069);
    --color-green-800: oklch(44.8% 0.119 151.328);
    --color-green-900: oklch(39.3% 0.095 152.535);
    --color-blue-100: oklch(93.2% 0.032 255.585);
    --color-blue-300: oklch(80.9% 0.105 251.813);
    --color-blue-400: oklch(70.7% 0.165 254.624);
    --color-blue-500: oklch(62.3% 0.214 259.815);
    --color-blue-700: oklch(48.8% 0.243 264.376);
    --color-blue-800: oklch(42.4% 0.199 265.638);
    --color-indigo-400: oklch(67.3% 0.182 276.935);
    --color-indigo-700: oklch(45.7% 0.24 277.023);
    --color-gray-50: oklch(98.5% 0.002 247.839);
    --color-gray-100: oklch(96.7% 0.003 264.542);
    --color-gray-200: oklch(92.8% 0.006 264.531);
    --color-gray-400: oklch(70.7% 0.022 261.325);
    --color-gray-500: oklch(55.1% 0.027 264.364);
    --color-gray-600: oklch(44.6% 0.03 256.802);
    --color-gray-700: oklch(37.3% 0.034 259.733);
    --color-gray-800: oklch(27.8% 0.033 256.848);
    --color-gray-900: oklch(21% 0.034 264.665);
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --container-md: 28rem;
    --container-2xl: 42rem;
    --container-4xl: 56rem;
    --container-5xl: 64rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --animate-spin: spin 1s linear infinite;
    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
    --color-primary: rgb(39 74 121);
    --color-on-primary: rgb(255 255 255);
    --color-primary-container: rgb(78 110 160);
    --color-on-primary-container: rgb(255 255 255);
    --color-secondary: rgb(83 95 116);
    --color-on-secondary: rgb(255 255 255);
    --color-secondary-container: rgb(220 231 255);
    --color-error: rgb(186 26 26);
    --color-background: rgb(250 249 253);
    --color-on-background: rgb(26 28 31);
    --color-surface: rgb(250 249 253);
    --color-on-surface: rgb(26 28 31);
    --color-surface-variant: rgb(223 226 236);
    --color-on-surface-variant: rgb(67 71 79);
    --color-outline-variant: rgb(195 198 208);
    --color-surface-container-lowest: rgb(255 255 255);
    --color-surface-container-low: rgb(244 243 248);
    --color-surface-container: rgb(238 237 242);
    --color-surface-container-high: rgb(232 232 236);
    --color-dark-primary: rgb(167 200 255);
    --color-dark-on-primary: rgb(4 48 95);
    --color-dark-primary-container: rgb(52 85 133);
    --color-dark-on-primary-container: rgb(246 247 255);
    --color-dark-secondary: rgb(187 199 223);
    --color-dark-on-secondary: rgb(37 49 68);
    --color-dark-secondary-container: rgb(52 63 83);
    --color-dark-error: rgb(255 180 171);
    --color-dark-background: rgb(18 19 23);
    --color-dark-on-background: rgb(227 226 230);
    --color-dark-surface: rgb(18 19 23);
    --color-dark-on-surface: rgb(227 226 230);
    --color-dark-surface-variant: rgb(67 71 79);
    --color-dark-on-surface-variant: rgb(195 198 208);
    --color-dark-outline-variant: rgb(67 71 79);
    --color-dark-surface-container-lowest: rgb(13 14 17);
    --color-dark-surface-container-low: rgb(26 28 31);
    --color-dark-surface-container: rgb(30 32 35);
    --color-dark-surface-container-high: rgb(41 42 45);
    --text-display-large: 4.1rem;
    --text-display-large--line-height: 5.2rem;
    --text-display-large--letter-spacing: -0.02em;
    --text-display-large--font-weight: 400;
    --text-headline-large: 2.4rem;
    --text-headline-large--line-height: 3.2rem;
    --text-headline-large--letter-spacing: 0em;
    --text-headline-large--font-weight: 400;
    --text-title-large: 1.4rem;
    --text-title-large--line-height: 2rem;
    --text-title-large--letter-spacing: 0em;
    --text-title-large--font-weight: 400;
    --text-title-medium: 1rem;
    --text-title-medium--line-height: 1.6rem;
    --text-title-medium--letter-spacing: 0.009rem;
    --text-title-medium--font-weight: 500;
    --text-body-large: 1rem;
    --text-body-large--line-height: 1.6rem;
    --text-body-large--letter-spacing: 0.031rem;
    --text-body-large--font-weight: 400;
    --text-label-large: 0.875rem;
    --text-label-large--line-height: 1.2rem;
    --text-label-large--letter-spacing: 0.012rem;
    --text-label-large--font-weight: 500;
    --text-label-small: 0.688rem;
    --text-label-small--line-height: 0.8rem;
    --text-label-small--letter-spacing: 0.012rem;
    --text-label-small--font-weight: 500;
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .invisible {
    visibility: hidden;
  }
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .sticky {
    position: sticky;
  }
  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }
  .start-0 {
    inset-inline-start: calc(var(--spacing) * 0);
  }
  .-top-2 {
    top: calc(var(--spacing) * -2);
  }
  .top-0 {
    top: calc(var(--spacing) * 0);
  }
  .top-4 {
    top: calc(var(--spacing) * 4);
  }
  .top-16 {
    top: calc(var(--spacing) * 16);
  }
  .top-full {
    top: 100%;
  }
  .-right-2 {
    right: calc(var(--spacing) * -2);
  }
  .right-4 {
    right: calc(var(--spacing) * 4);
  }
  .bottom-4 {
    bottom: calc(var(--spacing) * 4);
  }
  .left-4 {
    left: calc(var(--spacing) * 4);
  }
  .z-0 {
    z-index: 0;
  }
  .z-10 {
    z-index: 10;
  }
  .z-20 {
    z-index: 20;
  }
  .z-50 {
    z-index: 50;
  }
  .container {
    width: 100%;
    @media (width >= 40rem) {
      max-width: 40rem;
    }
    @media (width >= 48rem) {
      max-width: 48rem;
    }
    @media (width >= 64rem) {
      max-width: 64rem;
    }
    @media (width >= 80rem) {
      max-width: 80rem;
    }
    @media (width >= 96rem) {
      max-width: 96rem;
    }
  }
  .mx-auto {
    margin-inline: auto;
  }
  .my-6 {
    margin-block: calc(var(--spacing) * 6);
  }
  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }
  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }
  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-5 {
    margin-bottom: calc(var(--spacing) * 5);
  }
  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }
  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }
  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }
  .block {
    display: block;
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline-block {
    display: inline-block;
  }
  .inline-flex {
    display: inline-flex;
  }
  .size-6 {
    width: calc(var(--spacing) * 6);
    height: calc(var(--spacing) * 6);
  }
  .size-10 {
    width: calc(var(--spacing) * 10);
    height: calc(var(--spacing) * 10);
  }
  .size-10\! {
    width: calc(var(--spacing) * 10) !important;
    height: calc(var(--spacing) * 10) !important;
  }
  .size-14 {
    width: calc(var(--spacing) * 14);
    height: calc(var(--spacing) * 14);
  }
  .size-20 {
    width: calc(var(--spacing) * 20);
    height: calc(var(--spacing) * 20);
  }
  .size-64 {
    width: calc(var(--spacing) * 64);
    height: calc(var(--spacing) * 64);
  }
  .size-auto {
    width: auto;
    height: auto;
  }
  .size-full {
    width: 100%;
    height: 100%;
  }
  .h-1 {
    height: calc(var(--spacing) * 1);
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-5 {
    height: calc(var(--spacing) * 5);
  }
  .h-6 {
    height: calc(var(--spacing) * 6);
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .h-10 {
    height: calc(var(--spacing) * 10);
  }
  .h-12 {
    height: calc(var(--spacing) * 12);
  }
  .h-14 {
    height: calc(var(--spacing) * 14);
  }
  .h-16 {
    height: calc(var(--spacing) * 16);
  }
  .h-32 {
    height: calc(var(--spacing) * 32);
  }
  .h-\[50vh\] {
    height: 50vh;
  }
  .h-\[60vh\] {
    height: 60vh;
  }
  .h-\[400rem\] {
    height: 400rem;
  }
  .h-\[calc\(100\%-1rem\)\] {
    height: calc(100% - 1rem);
  }
  .h-\[calc\(100vh-4rem\)\] {
    height: calc(100vh - 4rem);
  }
  .h-\[calc\(100vh_-_4rem\)\] {
    height: calc(100vh - 4rem);
  }
  .h-full {
    height: 100%;
  }
  .max-h-28 {
    max-height: calc(var(--spacing) * 28);
  }
  .max-h-\[90vh\] {
    max-height: 90vh;
  }
  .max-h-\[calc\(100vh_-_4rem\)\] {
    max-height: calc(100vh - 4rem);
  }
  .max-h-full {
    max-height: 100%;
  }
  .min-h-screen {
    min-height: 100vh;
  }
  .w-1\/2 {
    width: calc(1/2 * 100%);
  }
  .w-1\/3 {
    width: calc(1/3 * 100%);
  }
  .w-2\/3 {
    width: calc(2/3 * 100%);
  }
  .w-3\/4 {
    width: calc(3/4 * 100%);
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-6 {
    width: calc(var(--spacing) * 6);
  }
  .w-12 {
    width: calc(var(--spacing) * 12);
  }
  .w-14 {
    width: calc(var(--spacing) * 14);
  }
  .w-20 {
    width: calc(var(--spacing) * 20);
  }
  .w-24 {
    width: calc(var(--spacing) * 24);
  }
  .w-32 {
    width: calc(var(--spacing) * 32);
  }
  .w-64 {
    width: calc(var(--spacing) * 64);
  }
  .w-\[25rem\] {
    width: 25rem;
  }
  .w-fit {
    width: fit-content;
  }
  .w-full {
    width: 100%;
  }
  .max-w-2xl {
    max-width: var(--container-2xl);
  }
  .max-w-4xl {
    max-width: var(--container-4xl);
  }
  .max-w-5xl {
    max-width: var(--container-5xl);
  }
  .max-w-md {
    max-width: var(--container-md);
  }
  .min-w-52 {
    min-width: calc(var(--spacing) * 52);
  }
  .min-w-\[18\.75rem\] {
    min-width: 18.75rem;
  }
  .min-w-full {
    min-width: 100%;
  }
  .flex-1 {
    flex: 1;
  }
  .flex-shrink-0 {
    flex-shrink: 0;
  }
  .flex-grow {
    flex-grow: 1;
  }
  .grow {
    flex-grow: 1;
  }
  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .transform {
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .animate-pulse {
    animation: var(--animate-pulse);
  }
  .animate-spin {
    animation: var(--animate-spin);
  }
  .cursor-not-allowed {
    cursor: not-allowed;
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .resize-none {
    resize: none;
  }
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .grid-cols-\[2fr_1fr_1fr\] {
    grid-template-columns: 2fr 1fr 1fr;
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .items-center {
    align-items: center;
  }
  .items-start {
    align-items: flex-start;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .justify-start {
    justify-content: flex-start;
  }
  .gap-0\.5 {
    gap: calc(var(--spacing) * 0.5);
  }
  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }
  .divide-y {
    :where(& > :not(:last-child)) {
      --tw-divide-y-reverse: 0;
      border-bottom-style: var(--tw-border-style);
      border-top-style: var(--tw-border-style);
      border-top-width: calc(1px * var(--tw-divide-y-reverse));
      border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
    }
  }
  .divide-outline-variant {
    :where(& > :not(:last-child)) {
      border-color: var(--color-outline-variant);
    }
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .overflow-x-auto {
    overflow-x: auto;
  }
  .overflow-x-hidden {
    overflow-x: hidden;
  }
  .overflow-y-auto {
    overflow-y: auto;
  }
  .rounded {
    border-radius: 0.25rem;
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-lg {
    border-radius: var(--radius-lg);
  }
  .rounded-md {
    border-radius: var(--radius-md);
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-l-4 {
    border-left-style: var(--tw-border-style);
    border-left-width: 4px;
  }
  .border-blue-500 {
    border-color: var(--color-blue-500);
  }
  .border-error {
    border-color: var(--color-error);
  }
  .border-gray-200 {
    border-color: var(--color-gray-200);
  }
  .border-green-200 {
    border-color: var(--color-green-200);
  }
  .border-green-300 {
    border-color: var(--color-green-300);
  }
  .border-outline-variant {
    border-color: var(--color-outline-variant);
  }
  .border-primary {
    border-color: var(--color-primary);
  }
  .border-red-200 {
    border-color: var(--color-red-200);
  }
  .border-red-500 {
    border-color: var(--color-red-500);
  }
  .bg-background {
    background-color: var(--color-background);
  }
  .bg-black\/50 {
    background-color: color-mix(in srgb, #000 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
    }
  }
  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }
  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }
  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }
  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }
  .bg-green-50 {
    background-color: var(--color-green-50);
  }
  .bg-green-100 {
    background-color: var(--color-green-100);
  }
  .bg-green-500 {
    background-color: var(--color-green-500);
  }
  .bg-primary {
    background-color: var(--color-primary);
  }
  .bg-primary-container {
    background-color: var(--color-primary-container);
  }
  .bg-red-50 {
    background-color: var(--color-red-50);
  }
  .bg-red-500 {
    background-color: var(--color-red-500);
  }
  .bg-secondary {
    background-color: var(--color-secondary);
  }
  .bg-secondary-container {
    background-color: var(--color-secondary-container);
  }
  .bg-surface-container {
    background-color: var(--color-surface-container);
  }
  .bg-surface-container-high {
    background-color: var(--color-surface-container-high);
  }
  .bg-surface-container-low {
    background-color: var(--color-surface-container-low);
  }
  .bg-surface-container-lowest {
    background-color: var(--color-surface-container-lowest);
  }
  .bg-transparent {
    background-color: transparent;
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .object-cover {
    object-fit: cover;
  }
  .object-fill {
    object-fit: fill;
  }
  .object-center {
    object-position: center;
  }
  .p-0\! {
    padding: calc(var(--spacing) * 0) !important;
  }
  .p-1 {
    padding: calc(var(--spacing) * 1);
  }
  .p-2 {
    padding: calc(var(--spacing) * 2);
  }
  .p-3 {
    padding: calc(var(--spacing) * 3);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .p-8 {
    padding: calc(var(--spacing) * 8);
  }
  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
  .py-0\.5 {
    padding-block: calc(var(--spacing) * 0.5);
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }
  .ps-0\! {
    padding-inline-start: calc(var(--spacing) * 0) !important;
  }
  .ps-2 {
    padding-inline-start: calc(var(--spacing) * 2);
  }
  .ps-3 {
    padding-inline-start: calc(var(--spacing) * 3);
  }
  .pe-0\! {
    padding-inline-end: calc(var(--spacing) * 0) !important;
  }
  .pe-4 {
    padding-inline-end: calc(var(--spacing) * 4);
  }
  .pb-3 {
    padding-bottom: calc(var(--spacing) * 3);
  }
  .text-center {
    text-align: center;
  }
  .text-left {
    text-align: left;
  }
  .text-right {
    text-align: right;
  }
  .align-middle {
    vertical-align: middle;
  }
  .align-top {
    vertical-align: top;
  }
  .text-body-large {
    font-size: var(--text-body-large);
    line-height: var(--tw-leading, var(--text-body-large--line-height));
    letter-spacing: var(--tw-tracking, var(--text-body-large--letter-spacing));
    font-weight: var(--tw-font-weight, var(--text-body-large--font-weight));
  }
  .text-display-large {
    font-size: var(--text-display-large);
    line-height: var(--tw-leading, var(--text-display-large--line-height));
    letter-spacing: var(--tw-tracking, var(--text-display-large--letter-spacing));
    font-weight: var(--tw-font-weight, var(--text-display-large--font-weight));
  }
  .text-headline-large {
    font-size: var(--text-headline-large);
    line-height: var(--tw-leading, var(--text-headline-large--line-height));
    letter-spacing: var(--tw-tracking, var(--text-headline-large--letter-spacing));
    font-weight: var(--tw-font-weight, var(--text-headline-large--font-weight));
  }
  .text-label-large {
    font-size: var(--text-label-large);
    line-height: var(--tw-leading, var(--text-label-large--line-height));
    letter-spacing: var(--tw-tracking, var(--text-label-large--letter-spacing));
    font-weight: var(--tw-font-weight, var(--text-label-large--font-weight));
  }
  .text-label-small {
    font-size: var(--text-label-small);
    line-height: var(--tw-leading, var(--text-label-small--line-height));
    letter-spacing: var(--tw-tracking, var(--text-label-small--letter-spacing));
    font-weight: var(--tw-font-weight, var(--text-label-small--font-weight));
  }
  .text-title-large {
    font-size: var(--text-title-large);
    line-height: var(--tw-leading, var(--text-title-large--line-height));
    letter-spacing: var(--tw-tracking, var(--text-title-large--letter-spacing));
    font-weight: var(--tw-font-weight, var(--text-title-large--font-weight));
  }
  .text-title-medium {
    font-size: var(--text-title-medium);
    line-height: var(--tw-leading, var(--text-title-medium--line-height));
    letter-spacing: var(--tw-tracking, var(--text-title-medium--letter-spacing));
    font-weight: var(--tw-font-weight, var(--text-title-medium--font-weight));
  }
  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .text-\[10px\] {
    font-size: 10px;
  }
  .text-\[11px\] {
    font-size: 11px;
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-extrabold {
    --tw-font-weight: var(--font-weight-extrabold);
    font-weight: var(--font-weight-extrabold);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .whitespace-nowrap {
    white-space: nowrap;
  }
  .text-blue-500 {
    color: var(--color-blue-500);
  }
  .text-blue-700 {
    color: var(--color-blue-700);
  }
  .text-error {
    color: var(--color-error);
  }
  .text-gray-400 {
    color: var(--color-gray-400);
  }
  .text-gray-500 {
    color: var(--color-gray-500);
  }
  .text-gray-600 {
    color: var(--color-gray-600);
  }
  .text-gray-900 {
    color: var(--color-gray-900);
  }
  .text-green-500 {
    color: var(--color-green-500);
  }
  .text-green-600 {
    color: var(--color-green-600);
  }
  .text-green-700 {
    color: var(--color-green-700);
  }
  .text-green-800 {
    color: var(--color-green-800);
  }
  .text-indigo-700 {
    color: var(--color-indigo-700);
  }
  .text-on-background {
    color: var(--color-on-background);
  }
  .text-on-primary {
    color: var(--color-on-primary);
  }
  .text-on-primary-container {
    color: var(--color-on-primary-container);
  }
  .text-on-secondary {
    color: var(--color-on-secondary);
  }
  .text-on-surface {
    color: var(--color-on-surface);
  }
  .text-on-surface-variant {
    color: var(--color-on-surface-variant);
  }
  .text-primary {
    color: var(--color-primary);
  }
  .text-primary-container {
    color: var(--color-primary-container);
  }
  .text-red-400 {
    color: var(--color-red-400);
  }
  .text-red-500 {
    color: var(--color-red-500);
  }
  .text-red-600 {
    color: var(--color-red-600);
  }
  .text-red-700 {
    color: var(--color-red-700);
  }
  .text-red-800 {
    color: var(--color-red-800);
  }
  .text-secondary {
    color: var(--color-secondary);
  }
  .text-white {
    color: var(--color-white);
  }
  .lowercase {
    text-transform: lowercase;
  }
  .uppercase {
    text-transform: uppercase;
  }
  .opacity-50 {
    opacity: 50%;
  }
  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-4 {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-surface {
    --tw-ring-color: var(--color-surface);
  }
  .ring-offset-4 {
    --tw-ring-offset-width: 4px;
    --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }
  .ring-offset-surface {
    --tw-ring-offset-color: var(--color-surface);
  }
  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .duration-200 {
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
  .duration-300 {
    --tw-duration: 300ms;
    transition-duration: 300ms;
  }
  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }
  .placeholder\:italic {
    &::placeholder {
      font-style: italic;
    }
  }
  .hover\:bg-dark-surface-variant {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-dark-surface-variant);
      }
    }
  }
  .hover\:bg-green-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-green-600);
      }
    }
  }
  .hover\:bg-primary-container {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-primary-container);
      }
    }
  }
  .hover\:bg-primary\/10 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in srgb, rgb(39 74 121) 10%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-primary) 10%, transparent);
        }
      }
    }
  }
  .hover\:bg-primary\/65 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in srgb, rgb(39 74 121) 65%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-primary) 65%, transparent);
        }
      }
    }
  }
  .hover\:bg-primary\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in srgb, rgb(39 74 121) 90%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-primary) 90%, transparent);
        }
      }
    }
  }
  .hover\:bg-red-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-50);
      }
    }
  }
  .hover\:bg-red-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-600);
      }
    }
  }
  .hover\:bg-secondary\/55 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in srgb, rgb(83 95 116) 55%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-secondary) 55%, transparent);
        }
      }
    }
  }
  .hover\:bg-surface-variant {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-surface-variant);
      }
    }
  }
  .hover\:text-blue-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-800);
      }
    }
  }
  .hover\:text-dark-on-surface-variant {
    &:hover {
      @media (hover: hover) {
        color: var(--color-dark-on-surface-variant);
      }
    }
  }
  .hover\:text-gray-700 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-700);
      }
    }
  }
  .hover\:text-green-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-green-800);
      }
    }
  }
  .hover\:text-on-surface-variant {
    &:hover {
      @media (hover: hover) {
        color: var(--color-on-surface-variant);
      }
    }
  }
  .hover\:text-red-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-red-600);
      }
    }
  }
  .hover\:text-red-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-red-800);
      }
    }
  }
  .hover\:underline {
    &:hover {
      @media (hover: hover) {
        text-decoration-line: underline;
      }
    }
  }
  .hover\:opacity-60 {
    &:hover {
      @media (hover: hover) {
        opacity: 60%;
      }
    }
  }
  .hover\:shadow-lg {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:shadow-xl {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .focus\:ring-2 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-primary {
    &:focus {
      --tw-ring-color: var(--color-primary);
    }
  }
  .focus\:ring-primary\/50 {
    &:focus {
      --tw-ring-color: color-mix(in srgb, rgb(39 74 121) 50%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--color-primary) 50%, transparent);
      }
    }
  }
  .focus\:ring-offset-2 {
    &:focus {
      --tw-ring-offset-width: 2px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
  }
  .focus\:ring-offset-background {
    &:focus {
      --tw-ring-offset-color: var(--color-background);
    }
  }
  .focus\:outline-none {
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .active\:scale-95 {
    &:active {
      --tw-scale-x: 95%;
      --tw-scale-y: 95%;
      --tw-scale-z: 95%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }
  .active\:bg-primary\/80 {
    &:active {
      background-color: color-mix(in srgb, rgb(39 74 121) 80%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-primary) 80%, transparent);
      }
    }
  }
  .disabled\:cursor-not-allowed {
    &:disabled {
      cursor: not-allowed;
    }
  }
  .disabled\:opacity-50 {
    &:disabled {
      opacity: 50%;
    }
  }
  .has-\[\:focus\]\:ring-error {
    &:has(*:is(:focus)) {
      --tw-ring-color: var(--color-error);
    }
  }
  .has-\[\:focus\]\:ring-primary {
    &:has(*:is(:focus)) {
      --tw-ring-color: var(--color-primary);
    }
  }
  .md\:inset-0 {
    @media (width >= 48rem) {
      inset: calc(var(--spacing) * 0);
    }
  }
  .md\:px-4 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 4);
    }
  }
  .rtl\:text-right {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      text-align: right;
    }
  }
  .dark\:divide-dark-outline-variant {
    &:where(.dark, .dark *) {
      :where(& > :not(:last-child)) {
        border-color: var(--color-dark-outline-variant);
      }
    }
  }
  .dark\:border-dark-error {
    &:where(.dark, .dark *) {
      border-color: var(--color-dark-error);
    }
  }
  .dark\:border-dark-outline-variant {
    &:where(.dark, .dark *) {
      border-color: var(--color-dark-outline-variant);
    }
  }
  .dark\:border-dark-primary {
    &:where(.dark, .dark *) {
      border-color: var(--color-dark-primary);
    }
  }
  .dark\:border-gray-700 {
    &:where(.dark, .dark *) {
      border-color: var(--color-gray-700);
    }
  }
  .dark\:border-green-800 {
    &:where(.dark, .dark *) {
      border-color: var(--color-green-800);
    }
  }
  .dark\:border-red-800 {
    &:where(.dark, .dark *) {
      border-color: var(--color-red-800);
    }
  }
  .dark\:bg-dark-background {
    &:where(.dark, .dark *) {
      background-color: var(--color-dark-background);
    }
  }
  .dark\:bg-dark-primary {
    &:where(.dark, .dark *) {
      background-color: var(--color-dark-primary);
    }
  }
  .dark\:bg-dark-primary-container {
    &:where(.dark, .dark *) {
      background-color: var(--color-dark-primary-container);
    }
  }
  .dark\:bg-dark-secondary {
    &:where(.dark, .dark *) {
      background-color: var(--color-dark-secondary);
    }
  }
  .dark\:bg-dark-secondary-container {
    &:where(.dark, .dark *) {
      background-color: var(--color-dark-secondary-container);
    }
  }
  .dark\:bg-dark-surface-container {
    &:where(.dark, .dark *) {
      background-color: var(--color-dark-surface-container);
    }
  }
  .dark\:bg-dark-surface-container-high {
    &:where(.dark, .dark *) {
      background-color: var(--color-dark-surface-container-high);
    }
  }
  .dark\:bg-dark-surface-container-low {
    &:where(.dark, .dark *) {
      background-color: var(--color-dark-surface-container-low);
    }
  }
  .dark\:bg-dark-surface-container-lowest {
    &:where(.dark, .dark *) {
      background-color: var(--color-dark-surface-container-lowest);
    }
  }
  .dark\:bg-gray-700 {
    &:where(.dark, .dark *) {
      background-color: var(--color-gray-700);
    }
  }
  .dark\:bg-gray-800 {
    &:where(.dark, .dark *) {
      background-color: var(--color-gray-800);
    }
  }
  .dark\:bg-green-900\/50 {
    &:where(.dark, .dark *) {
      background-color: color-mix(in srgb, oklch(39.3% 0.095 152.535) 50%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-green-900) 50%, transparent);
      }
    }
  }
  .dark\:bg-red-900\/50 {
    &:where(.dark, .dark *) {
      background-color: color-mix(in srgb, oklch(39.6% 0.141 25.723) 50%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-red-900) 50%, transparent);
      }
    }
  }
  .dark\:text-blue-400 {
    &:where(.dark, .dark *) {
      color: var(--color-blue-400);
    }
  }
  .dark\:text-dark-error {
    &:where(.dark, .dark *) {
      color: var(--color-dark-error);
    }
  }
  .dark\:text-dark-on-background {
    &:where(.dark, .dark *) {
      color: var(--color-dark-on-background);
    }
  }
  .dark\:text-dark-on-primary {
    &:where(.dark, .dark *) {
      color: var(--color-dark-on-primary);
    }
  }
  .dark\:text-dark-on-primary-container {
    &:where(.dark, .dark *) {
      color: var(--color-dark-on-primary-container);
    }
  }
  .dark\:text-dark-on-secondary {
    &:where(.dark, .dark *) {
      color: var(--color-dark-on-secondary);
    }
  }
  .dark\:text-dark-on-surface {
    &:where(.dark, .dark *) {
      color: var(--color-dark-on-surface);
    }
  }
  .dark\:text-dark-on-surface-variant {
    &:where(.dark, .dark *) {
      color: var(--color-dark-on-surface-variant);
    }
  }
  .dark\:text-dark-primary {
    &:where(.dark, .dark *) {
      color: var(--color-dark-primary);
    }
  }
  .dark\:text-dark-primary-container {
    &:where(.dark, .dark *) {
      color: var(--color-dark-primary-container);
    }
  }
  .dark\:text-dark-secondary {
    &:where(.dark, .dark *) {
      color: var(--color-dark-secondary);
    }
  }
  .dark\:text-gray-100 {
    &:where(.dark, .dark *) {
      color: var(--color-gray-100);
    }
  }
  .dark\:text-gray-400 {
    &:where(.dark, .dark *) {
      color: var(--color-gray-400);
    }
  }
  .dark\:text-green-200 {
    &:where(.dark, .dark *) {
      color: var(--color-green-200);
    }
  }
  .dark\:text-green-400 {
    &:where(.dark, .dark *) {
      color: var(--color-green-400);
    }
  }
  .dark\:text-indigo-400 {
    &:where(.dark, .dark *) {
      color: var(--color-indigo-400);
    }
  }
  .dark\:text-red-200 {
    &:where(.dark, .dark *) {
      color: var(--color-red-200);
    }
  }
  .dark\:text-red-400 {
    &:where(.dark, .dark *) {
      color: var(--color-red-400);
    }
  }
  .dark\:text-white {
    &:where(.dark, .dark *) {
      color: var(--color-white);
    }
  }
  .dark\:ring-dark-surface {
    &:where(.dark, .dark *) {
      --tw-ring-color: var(--color-dark-surface);
    }
  }
  .dark\:ring-offset-dark-surface {
    &:where(.dark, .dark *) {
      --tw-ring-offset-color: var(--color-dark-surface);
    }
  }
  .dark\:hover\:bg-dark-primary-container {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-dark-primary-container);
        }
      }
    }
  }
  .dark\:hover\:bg-dark-primary\/10 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: color-mix(in srgb, rgb(167 200 255) 10%, transparent);
          @supports (color: color-mix(in lab, red, red)) {
            background-color: color-mix(in oklab, var(--color-dark-primary) 10%, transparent);
          }
        }
      }
    }
  }
  .dark\:hover\:bg-dark-primary\/65 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: color-mix(in srgb, rgb(167 200 255) 65%, transparent);
          @supports (color: color-mix(in lab, red, red)) {
            background-color: color-mix(in oklab, var(--color-dark-primary) 65%, transparent);
          }
        }
      }
    }
  }
  .dark\:hover\:bg-green-400 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-green-400);
        }
      }
    }
  }
  .dark\:hover\:bg-red-400 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-red-400);
        }
      }
    }
  }
  .hover\:dark\:bg-dark-secondary\/55 {
    &:hover {
      @media (hover: hover) {
        &:where(.dark, .dark *) {
          background-color: color-mix(in srgb, rgb(187 199 223) 55%, transparent);
          @supports (color: color-mix(in lab, red, red)) {
            background-color: color-mix(in oklab, var(--color-dark-secondary) 55%, transparent);
          }
        }
      }
    }
  }
  .dark\:hover\:text-blue-300 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          color: var(--color-blue-300);
        }
      }
    }
  }
  .dark\:hover\:text-dark-on-surface-variant {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          color: var(--color-dark-on-surface-variant);
        }
      }
    }
  }
  .dark\:hover\:text-gray-200 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          color: var(--color-gray-200);
        }
      }
    }
  }
  .dark\:hover\:text-green-300 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          color: var(--color-green-300);
        }
      }
    }
  }
  .dark\:hover\:text-red-300 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          color: var(--color-red-300);
        }
      }
    }
  }
  .dark\:focus\:ring-dark-primary\/50 {
    &:where(.dark, .dark *) {
      &:focus {
        --tw-ring-color: color-mix(in srgb, rgb(167 200 255) 50%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-ring-color: color-mix(in oklab, var(--color-dark-primary) 50%, transparent);
        }
      }
    }
  }
  .dark\:focus\:ring-offset-dark-background {
    &:where(.dark, .dark *) {
      &:focus {
        --tw-ring-offset-color: var(--color-dark-background);
      }
    }
  }
  .dark\:has-\[\:focus\]\:ring-dark-error {
    &:where(.dark, .dark *) {
      &:has(*:is(:focus)) {
        --tw-ring-color: var(--color-dark-error);
      }
    }
  }
  .dark\:has-\[\:focus\]\:ring-dark-primary {
    &:where(.dark, .dark *) {
      &:has(*:is(:focus)) {
        --tw-ring-color: var(--color-dark-primary);
      }
    }
  }
}
@layer base {
  :root {
    --font-cairo: 'Cairo', sans-serif;
  }
  html {
    font-family: var(--font-cairo);
  }
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-ease {
  syntax: "*";
  inherits: false;
}
@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
@keyframes pulse {
  50% {
    opacity: 0.5;
  }
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-divide-y-reverse: 0;
      --tw-border-style: solid;
      --tw-font-weight: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
    }
  }
}

