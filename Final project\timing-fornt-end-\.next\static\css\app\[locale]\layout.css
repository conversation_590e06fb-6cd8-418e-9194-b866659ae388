/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\[locale]\\layout.tsx","import":"Cairo","arguments":[{"subsets":["arabic","latin"],"display":"swap","variable":"--font-cairo","preload":true,"weight":["200","300","400","500","600","700","800","900"]}],"variableName":"cairo"} ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@font-face {
  font-family: 'Cairo Fallback';
  src: local("Arial");
  ascent-override:137.65%;
  descent-override:60.32%;
  line-gap-override:0.00%;
  size-adjust:94.66%;
}@font-face {font-family: 'Cairo Fallback Fallback';src: local("Arial");ascent-override: 137.65%;descent-override: 60.32%;line-gap-override: 0.00%;size-adjust: 94.66%
}.__className_d7c199 {font-family: 'Cairo Fallback', 'Cairo Fallback Fallback';font-style: normal
}.__variable_d7c199 {--font-cairo: 'Cairo Fallback', 'Cairo Fallback Fallback'
}
