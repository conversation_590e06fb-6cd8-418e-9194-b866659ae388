'use server'

import axiosInstance from '@/lib/server/tools/axios'

export interface Baladiya {
    id: number;
    name: string;
    wilaya_id: number;
    created_at: string;
    updated_at: string;
    wilaya?: {
        id: number;
        name: string;
    };
}

export interface BaladiyaResponse {
    baladiyas: Baladiya[];
}

export async function getBaladiyas(): Promise<BaladiyaResponse> {
    try {
        const { data } = await axiosInstance.get<BaladiyaResponse>('/baladiyas')
        return data
    } catch (error: any) {
        console.error('Error fetching baladiyas:', error.response?.data)
        throw error
    }
}
