"use server"

import axiosInstance from "../../tools/axios";
import { ClassRoomPayload, ClassroomsResponse } from "../../types/validClasses/ValidClasses";
import { DaysResponse } from "../../types/days/days";
import { ModulesAllResponse } from "../../types/modulesAll/ModulesAll";
import { TeachersResponse } from "../../types/teachersAll/teachersAll";
import { TimeTableResponse } from "../../types/sectionTiming/sectionTiming";
import { revalidatePath } from "next/cache";

export async function getGroupTiming(groupId: number): Promise<TimeTableResponse> {
    try {
        const { data } = await axiosInstance.get(`groups/${groupId}/time-table`);
        return data;
    } catch (error) {
        console.error("Error fetching group timing:", error.response.data);
        throw error;
    }
}

export async function validClassRoom(groupId: number, data_payload: ClassRoomPayload): Promise<ClassroomsResponse> {
    try {
        const { data } = await axiosInstance.post(`groups/${groupId}/valid-classes`, data_payload);
        return data;
    } catch (error) {
        console.error("Error fetching group timing:", error.response.data);
        throw error;
    }
}

export async function reserveClassRome(groupId: number, data_payload: ClassRoomPayload): Promise<{ message: string, success: boolean }> {
    try {
        console.log('Creating lesson via admin for group:', groupId, data_payload);
        await axiosInstance.post(`groups/${groupId}/reserve-class-rome`, data_payload);

        // Comprehensive cache invalidation for all affected areas
        console.log('Invalidating cache after admin lesson creation...');

        // Teacher timing paths (most important for this fix)
        revalidatePath('/', 'layout') // Teacher guest timing page
        revalidatePath('/teacher')

        // Group timing paths
        revalidatePath('/dashboard/groups')
        revalidatePath('/dashboard/groups/timing')
        revalidatePath(`/dashboard/groups/timing/${groupId}`)

        // Student timing paths
        revalidatePath('/student')

        // General dashboard
        revalidatePath('/dashboard')

        console.log('Cache invalidated successfully after admin lesson creation');

        return {
            message: "Class reserved successfully",
            success: true
        };
    } catch (error) {
        console.error("Error creating lesson via admin:", error.response?.data || error);
        return {
            message: "Class not reserved",
            success: false
        };
    }
}

export async function deleteSession(sessionId: number): Promise<{ message: string, success: boolean }> {
    try {
        console.log('Deleting lesson:', sessionId);
        await axiosInstance.delete(`lessens/${sessionId}`);

        // Invalidate cache after lesson deletion
        console.log('Invalidating cache after lesson deletion...');
        revalidatePath('/', 'layout') // Teacher guest timing page
        revalidatePath('/teacher')
        revalidatePath('/dashboard/groups')
        revalidatePath('/dashboard/groups/timing')
        revalidatePath('/student')
        revalidatePath('/dashboard')
        console.log('Cache invalidated after lesson deletion');

        return {
            message: "Session deleted successfully",
            success: true
        };
    } catch (error) {
        console.error("Error deleting session:", error.response?.data || error);
        return {
            message: "Session not deleted",
            success: false
        };
    }
}

export async function getDays(groupId: number): Promise<DaysResponse> {
    try {
        const { data } = await axiosInstance.get(`group-days/${groupId}`);
        return data;
    } catch (error) {
        console.error("Error creating lessen timing:", error.response.data);
        throw error;
    }
}

export async function getModules(): Promise<ModulesAllResponse> {
    try {
        const { data } = await axiosInstance.get(`modulesAll`);
        return data;
    } catch (error) {
        console.error("Error creating lessen timing:", error.response.data);
        throw error;
    }
}

export async function getTeachers(): Promise<TeachersResponse> {
    try {
        const { data } = await axiosInstance.get(`teachersAll`);
        return data;
    } catch (error) {
        console.error("Error creating lessen timing:", error.response.data);
        throw error;
    }
}
