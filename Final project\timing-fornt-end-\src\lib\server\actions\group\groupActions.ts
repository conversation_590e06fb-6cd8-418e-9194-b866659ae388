'use server'

import { Group, GroupErrorResponse, CreateGroupRequest } from '@/lib/server/types/group/group'
import axiosInstance from '@/lib/server/tools/axios'
import { revalidatePath } from 'next/cache'
import { TimeTableStudent } from '../../types/sectionTiming/studentGroupTiming'
import { LessonsResponse, TeacherTimingResponse } from '../../types/sectionTiming/teacherTiming'

export async function createGroup(groupData: CreateGroupRequest): Promise<Group | GroupErrorResponse> {
    try {
        const { data } = await axiosInstance.post<Group>(
            `/groups`,
            groupData
        )
        // Revalidate the groups page and related paths
        revalidatePath('/dashboard/groups')
        revalidatePath('/dashboard/groups', 'page')
        revalidatePath('/dashboard')
        return data
    } catch (error: any) {
        if (error.response?.data) {
            return error.response.data as GroupErrorResponse
        }
        throw error
    }
}

export async function updateGroup(id: number, groupData: Partial<Group>): Promise<Group | GroupErrorResponse> {
    try {
        const { data } = await axiosInstance.put<Group>(
            `/groups/${id}`,
            groupData
        )
        // Revalidate the groups page and related paths
        revalidatePath('/dashboard/groups')
        revalidatePath('/dashboard/groups', 'page')
        revalidatePath('/dashboard')
        return data
    } catch (error: any) {
        if (error.response?.data) {
            return error.response.data as GroupErrorResponse
        }
        throw error
    }
}

export async function deleteGroup(id: number): Promise<{ success: boolean }> {
    try {
        await axiosInstance.delete(`/groups/${id}`)
        // Revalidate the groups page and related paths
        revalidatePath('/dashboard/groups')
        revalidatePath('/dashboard/groups', 'page')
        revalidatePath('/dashboard')
        return { success: true }
    } catch (error) {
        console.error('Error deleting group:', error)
        throw error
    }
}

export async function createGroupKey(id: number): Promise<{ success: boolean; key?: string }> {
    try {
        await axiosInstance.post<{ key: string }>(`/groups/${id}/generate-key`)
        // Revalidate the groups page and related paths
        revalidatePath('/dashboard/groups')
        revalidatePath('/dashboard/groups', 'page')
        revalidatePath('/dashboard')
        return { success: true }
    } catch (error: any) {
        console.error('Error creating group key:', error.response?.data)
        throw error
    }
}

export async function getGroups(): Promise<TimeTableStudent> {
    try {
        const { data } = await axiosInstance.get<TimeTableStudent>('/group/student')
        console.log('Student Group Timetable API Response:', data);

        if (!data.timeTableGroup) {
            console.error('Missing group timetable data in response:', data);
            throw new Error('Missing group timetable data');
        }

        if (!data.timeTableGroup.days) {
            console.error('Missing days data in group timetable:', {
                groupDays: data.timeTableGroup.days
            });
            throw new Error('Missing group days data');
        }

        if (!data.groupInfo) {
            console.error('Missing group info in response:', data);
            throw new Error('Missing group information');
        }

        return data;
    } catch (error: any) {
        console.error('Error getting student group timetable:', error.response?.data || error.message);
        throw error;
    }
}

export async function getTeacherTiming(): Promise<TeacherTimingResponse> {
    try {
        console.log('Fetching teacher timing data...');
        const { data } = await axiosInstance.get<TeacherTimingResponse>('/group/teacher')
        console.log('Teacher timing data fetched:', data);
        return data
    } catch (error: any) {
        console.error('Error getting teacher timing:', error.response?.data)
        throw error
    }
}

export async function refreshTeacherTiming(): Promise<void> {
    try {
        console.log('Force refreshing teacher timing data...');
        // Force revalidate teacher timing paths
        revalidatePath('/', 'layout')
        revalidatePath('/teacher')
        console.log('Teacher timing cache invalidated');
    } catch (error: any) {
        console.error('Error refreshing teacher timing:', error)
    }
}