"use client";

import { Input } from "@/lib/ui/components/global/Inputs/inputs";
import { createTeacher } from "@/lib/server/actions/teacher/teacherActions";
import { getBaladiyas, Baladiya } from "@/lib/server/actions/baladiya/baladiyaActions";
import { useForm } from "react-hook-form";
import Button from "@/lib/ui/components/global/Buttons/Button";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState, useEffect } from "react";
import { CheckCircle2, AlertCircle } from "lucide-react";
import { CreateTeacherRequest, TeacherErrorResponse } from "@/lib/server/types/teacher/teacher";
import { SimpleSelect } from "../../components/global/Inputs/SimpleSelect";

const createTeacherSchema = z.object({
    username: z.string()
        .min(1, "Username is required")
        .regex(/^[a-z0-9_]+$/, "Username can only contain lowercase letters, numbers, and underscores"),
    name: z.string()
        .min(1, "Name is required")
        .regex(/^[A-Z]/, "First letter must be capital")
        .regex(/^[A-Z][a-z]*$/, "Only letters are allowed"),
    last: z.string()
        .min(1, "Last name is required")
        .regex(/^[A-Z]/, "First letter must be capital")
        .regex(/^[A-Z][a-z]*$/, "Only letters are allowed"),
    date_of_birth: z.string().min(1, "Date of birth is required"),
    grade: z.string().optional(),
    research_field: z.string().optional(),
    baladiya_id: z.string().min(1, "Please select a Baladiya"),
});

type CreateTeacherFormData = z.infer<typeof createTeacherSchema>;

interface CreateTeacherFormProps {
    onSuccess?: () => void;
}

export default function CreateTeacherForm({ onSuccess }: CreateTeacherFormProps) {
    const [error, setError] = useState<string | null>(null);
    const [baladiyas, setBaladiyas] = useState<Baladiya[]>([]);
    const {
        register,
        handleSubmit,
        formState: { errors, isSubmitting, isSubmitSuccessful },
        reset,
    } = useForm<CreateTeacherFormData>({
        resolver: zodResolver(createTeacherSchema),
    });

    useEffect(() => {
        const fetchBaladiyas = async () => {
            try {
                const data = await getBaladiyas();
                setBaladiyas(data.baladiyas);
            } catch (error) {
                console.error('Error fetching baladiyas:', error);
                setError('Failed to load baladiyas');
            }
        };

        fetchBaladiyas();
    }, []);

    const onSubmit = async (data: CreateTeacherFormData) => {
        try {
            setError(null);
            const teacherData: CreateTeacherRequest = {
                username: data.username,
                name: data.name,
                last: data.last,
                date_of_birth: data.date_of_birth,
                grade: data.grade,
                research_field: data.research_field,
                baladiya_id: Number(data.baladiya_id),
            };

            const response = await createTeacher(teacherData);

            // Check if the result is an error response
            if ('message' in response && 'errors' in response) {
                setError(response.message);
                return;
            }

            // Success - reset form and call onSuccess
            reset();
            onSuccess?.();
        } catch (error: any) {
            console.error('Error creating teacher:', error);
            setError(error.message || 'Failed to create teacher');
        }
    };

    return (
        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4 w-full max-w-md">
            {error && (
                <div className="flex items-center gap-2 text-red-600 dark:text-red-400 animate-fade-in">
                    <AlertCircle size={20} />
                    <span>{error}</span>
                </div>
            )}
            {isSubmitSuccessful && !error && (
                <div className="flex items-center gap-2 text-green-600 dark:text-green-400 animate-fade-in">
                    <CheckCircle2 size={20} />
                    <span>Teacher created successfully!</span>
                </div>
            )}
            <Input
                label="username"
                title="Username"
                placeholder="Enter username (lowercase letters, numbers, underscores)"
                error={errors.username?.message}
                register={register}
            />
            <Input
                label="name"
                title="Name"
                placeholder="Enter name (First letter capital)"
                error={errors.name?.message}
                register={register}
            />
            <Input
                label="last"
                title="Last Name"
                placeholder="Enter last name (First letter capital)"
                error={errors.last?.message}
                register={register}
            />
            <Input
                label="date_of_birth"
                title="Date of birth"
                type="date"
                error={errors.date_of_birth?.message}
                register={register}
            />
            <Input
                label="grade"
                title="Grade"
                placeholder="Enter grade (optional)"
                error={errors.grade?.message}
                register={register}
            />
            <Input
                label="research_field"
                title="Research Field"
                placeholder="Enter research field (optional)"
                error={errors.research_field?.message}
                register={register}
            />

            <SimpleSelect
                title="Baladiya"
                label="baladiya_id"
                register={register("baladiya_id")}
                error={errors.baladiya_id?.message}
            >
                <option value="">Select Baladiya</option>
                {baladiyas.map((baladiya) => (
                    <option key={baladiya.id} value={baladiya.id}>
                        {baladiya.name} {baladiya.wilaya ? `- ${baladiya.wilaya.name}` : ''}
                    </option>
                ))}
            </SimpleSelect>

            <Button
                type="submit"
                mode="filled"
                disabled={isSubmitting}
            >
                {isSubmitting ? "Creating..." : "Create Teacher"}
            </Button>
        </form>
    );
}